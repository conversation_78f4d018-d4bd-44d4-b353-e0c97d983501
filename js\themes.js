// 主题系统配置和管理
const THEMES = {
    cyber: {
        name: '赛博朋克',
        description: '霓虹蓝科技风格',
        colors: {
            primary: '#00ccff',
            primaryLight: '#33d6ff',
            secondary: '#0f1622',
            accent: '#ff3c78',
            text: '#e6f2ff',
            textMuted: '#8599b2',
            border: 'rgba(0, 204, 255, 0.15)',
            pageGradientStart: '#0f1622',
            pageGradientEnd: '#192231',
            cardGradientStart: '#121b29',
            cardGradientEnd: '#1c2939',
            cardAccent: 'rgba(0, 204, 255, 0.12)',
            cardHoverBorder: 'rgba(0, 204, 255, 0.5)'
        },
        backgroundPattern: `
            radial-gradient(circle at 25px 25px, rgba(0, 204, 255, 0.04) 2px, transparent 3px),
            radial-gradient(circle at 75px 75px, rgba(255, 60, 120, 0.02) 1px, transparent 2px),
            radial-gradient(circle at 50px 50px, rgba(150, 255, 250, 0.015) 1px, transparent 2px)
        `
    },
    dark: {
        name: '深色经典',
        description: '经典深色主题',
        colors: {
            primary: '#3b82f6',
            primaryLight: '#60a5fa',
            secondary: '#1f2937',
            accent: '#f59e0b',
            text: '#f9fafb',
            textMuted: '#9ca3af',
            border: 'rgba(59, 130, 246, 0.2)',
            pageGradientStart: '#111827',
            pageGradientEnd: '#1f2937',
            cardGradientStart: '#1f2937',
            cardGradientEnd: '#374151',
            cardAccent: 'rgba(59, 130, 246, 0.1)',
            cardHoverBorder: 'rgba(59, 130, 246, 0.4)'
        },
        backgroundPattern: `
            radial-gradient(circle at 20px 20px, rgba(59, 130, 246, 0.03) 1px, transparent 2px)
        `
    },
    light: {
        name: '浅色清新',
        description: '清新浅色主题',
        colors: {
            primary: '#2563eb',
            primaryLight: '#3b82f6',
            secondary: '#f8fafc',
            accent: '#dc2626',
            text: '#1f2937',
            textMuted: '#6b7280',
            border: 'rgba(37, 99, 235, 0.2)',
            pageGradientStart: '#ffffff',
            pageGradientEnd: '#f1f5f9',
            cardGradientStart: '#ffffff',
            cardGradientEnd: '#f8fafc',
            cardAccent: 'rgba(37, 99, 235, 0.05)',
            cardHoverBorder: 'rgba(37, 99, 235, 0.3)'
        },
        backgroundPattern: `
            radial-gradient(circle at 30px 30px, rgba(37, 99, 235, 0.02) 1px, transparent 2px)
        `
    },
    purple: {
        name: '紫色梦幻',
        description: '神秘紫色主题',
        colors: {
            primary: '#8b5cf6',
            primaryLight: '#a78bfa',
            secondary: '#1e1b4b',
            accent: '#f59e0b',
            text: '#f3f4f6',
            textMuted: '#a1a1aa',
            border: 'rgba(139, 92, 246, 0.2)',
            pageGradientStart: '#1e1b4b',
            pageGradientEnd: '#312e81',
            cardGradientStart: '#312e81',
            cardGradientEnd: '#3730a3',
            cardAccent: 'rgba(139, 92, 246, 0.1)',
            cardHoverBorder: 'rgba(139, 92, 246, 0.4)'
        },
        backgroundPattern: `
            radial-gradient(circle at 40px 40px, rgba(139, 92, 246, 0.03) 2px, transparent 3px),
            radial-gradient(circle at 80px 80px, rgba(245, 158, 11, 0.02) 1px, transparent 2px)
        `
    },
    green: {
        name: '自然绿色',
        description: '清新自然主题',
        colors: {
            primary: '#10b981',
            primaryLight: '#34d399',
            secondary: '#064e3b',
            accent: '#f59e0b',
            text: '#ecfdf5',
            textMuted: '#a7f3d0',
            border: 'rgba(16, 185, 129, 0.2)',
            pageGradientStart: '#064e3b',
            pageGradientEnd: '#065f46',
            cardGradientStart: '#065f46',
            cardGradientEnd: '#047857',
            cardAccent: 'rgba(16, 185, 129, 0.1)',
            cardHoverBorder: 'rgba(16, 185, 129, 0.4)'
        },
        backgroundPattern: `
            radial-gradient(circle at 35px 35px, rgba(16, 185, 129, 0.03) 2px, transparent 3px)
        `
    },
    orange: {
        name: '温暖橙色',
        description: '温暖活力主题',
        colors: {
            primary: '#ea580c',
            primaryLight: '#fb923c',
            secondary: '#7c2d12',
            accent: '#3b82f6',
            text: '#fed7aa',
            textMuted: '#fdba74',
            border: 'rgba(234, 88, 12, 0.2)',
            pageGradientStart: '#7c2d12',
            pageGradientEnd: '#9a3412',
            cardGradientStart: '#9a3412',
            cardGradientEnd: '#c2410c',
            cardAccent: 'rgba(234, 88, 12, 0.1)',
            cardHoverBorder: 'rgba(234, 88, 12, 0.4)'
        },
        backgroundPattern: `
            radial-gradient(circle at 45px 45px, rgba(234, 88, 12, 0.03) 2px, transparent 3px)
        `
    }
};

// 主题管理类
class ThemeManager {
    constructor() {
        this.currentTheme = this.loadTheme();
        this.init();
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.setupThemeSelector();
    }

    loadTheme() {
        return localStorage.getItem('selectedTheme') || 'cyber';
    }

    saveTheme(themeName) {
        localStorage.setItem('selectedTheme', themeName);
    }

    applyTheme(themeName) {
        const theme = THEMES[themeName];
        if (!theme) return;

        const root = document.documentElement;
        
        // 应用CSS变量
        Object.entries(theme.colors).forEach(([key, value]) => {
            const cssVar = key.replace(/([A-Z])/g, '-$1').toLowerCase();
            root.style.setProperty(`--${cssVar}`, value);
        });

        // 应用背景图案
        root.style.setProperty('--background-pattern', theme.backgroundPattern);

        this.currentTheme = themeName;
        this.saveTheme(themeName);

        // 更新主题选择器的显示
        this.updateThemeSelector();
    }

    setupThemeSelector() {
        // 在设置面板中添加主题选择器
        const settingsPanel = document.getElementById('settingsPanel');
        if (!settingsPanel) return;

        // 检查是否已经存在主题选择器
        let themeSection = document.getElementById('themeSection');
        if (!themeSection) {
            themeSection = document.createElement('div');
            themeSection.id = 'themeSection';
            themeSection.className = 'mb-6';
            themeSection.innerHTML = `
                <h3 class="text-lg font-semibold mb-3 text-white">主题选择</h3>
                <div id="themeGrid" class="grid grid-cols-2 gap-3">
                    <!-- 主题选项将在这里动态生成 -->
                </div>
            `;
            
            // 插入到设置面板的适当位置
            const firstSection = settingsPanel.querySelector('.mb-6');
            if (firstSection) {
                settingsPanel.insertBefore(themeSection, firstSection);
            } else {
                settingsPanel.appendChild(themeSection);
            }
        }

        this.renderThemeOptions();
    }

    renderThemeOptions() {
        const themeGrid = document.getElementById('themeGrid');
        if (!themeGrid) return;

        themeGrid.innerHTML = '';

        Object.entries(THEMES).forEach(([key, theme]) => {
            const themeOption = document.createElement('div');
            themeOption.className = `theme-option cursor-pointer p-3 rounded-lg border-2 transition-all duration-300 ${
                this.currentTheme === key ? 'border-white' : 'border-gray-600 hover:border-gray-400'
            }`;
            themeOption.style.background = `linear-gradient(135deg, ${theme.colors.cardGradientStart}, ${theme.colors.cardGradientEnd})`;
            
            themeOption.innerHTML = `
                <div class="flex items-center space-x-2 mb-2">
                    <div class="w-4 h-4 rounded-full" style="background: ${theme.colors.primary}"></div>
                    <div class="w-3 h-3 rounded-full" style="background: ${theme.colors.accent}"></div>
                </div>
                <div class="text-sm font-medium text-white">${theme.name}</div>
                <div class="text-xs text-gray-300 mt-1">${theme.description}</div>
            `;

            themeOption.addEventListener('click', () => {
                this.applyTheme(key);
            });

            themeGrid.appendChild(themeOption);
        });
    }

    updateThemeSelector() {
        const themeOptions = document.querySelectorAll('.theme-option');
        themeOptions.forEach((option, index) => {
            const themeKey = Object.keys(THEMES)[index];
            if (themeKey === this.currentTheme) {
                option.classList.remove('border-gray-600');
                option.classList.add('border-white');
            } else {
                option.classList.remove('border-white');
                option.classList.add('border-gray-600');
            }
        });
    }

    getThemeList() {
        return Object.entries(THEMES).map(([key, theme]) => ({
            key,
            name: theme.name,
            description: theme.description
        }));
    }

    getCurrentTheme() {
        return this.currentTheme;
    }
}

// 全局主题管理器实例
let themeManager;

// 初始化主题系统
function initThemeSystem() {
    themeManager = new ThemeManager();
}

// 导出给其他模块使用
window.ThemeManager = ThemeManager;
window.initThemeSystem = initThemeSystem;
window.themeManager = themeManager;
