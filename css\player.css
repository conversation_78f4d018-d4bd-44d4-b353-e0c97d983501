body, html {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    background-color: #0f1622;
    color: white;
    padding-top: 37px;
}

/* Critical header and navigation styles */
.player-header {
    position: relative;
    z-index: 2147483647 !important;
    pointer-events: auto !important;
}

.player-header-fixed {
    position: fixed !important;
    top: 0;
    left: 0;
    width: 100vw;
    z-index: 9000 !important;
    pointer-events: auto !important;
    background: #111;
}

#homeButton { 
    pointer-events: auto !important; 
}

.home-button {
    background: none !important;
    border: none !important;
    padding: 0 !important;
}

/* Critical loading styles to prevent FOUC */
.player-placeholder {
    width: 100% !important;
    height: auto !important;
    aspect-ratio: 16/9 !important;
    background-color: #1f2937 !important;
    position: relative !important;
    display: block !important;
    border-radius: 8px !important;
    overflow: hidden !important;
}

.player-loading-container {
    width: 100% !important;
    height: 0 !important;
    padding-bottom: 56.25% !important;
    position: relative !important;
    background-color: #1f2937 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    display: block !important;
}

.player-loading-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: rgba(17, 24, 39, 0.7) !important;
}

.player-loading-spinner {
    width: 48px !important;
    height: 48px !important;
    border: 4px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 50% !important;
    border-top-color: #f97316 !important;
    position: relative !important;
    margin-bottom: 16px !important;
    animation: spin 1s linear infinite !important;
}

/* Critical styles for loading text */
.player-loading-text,
.player-loading-overlay div:nth-child(2),
div.player-loading-text {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: #f9fafb !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    margin-bottom: 8px !important;
    text-align: center !important;
    font-family: system-ui, -apple-system, sans-serif !important;
    line-height: 1.4 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    pointer-events: none !important;
    z-index: 100 !important;
    background: transparent !important;
    max-width: 90% !important;
}

@keyframes player-spinner-rotate {
    to { transform: rotate(360deg); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ArtPlayer specific styles */
.art-video-player, .art-video-player video {
    width: 100% !important;
    height: 100% !important;
    min-height: 150px !important;
    max-height: 100vh !important;
    background: #000 !important;
    object-fit: contain !important;
    display: block !important;
}

/* Fix for Chrome-specific issues */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    .art-video-player video {
        transform: translateZ(0) !important;
        will-change: transform !important;
    }

    /* Force visibility of video element */
    .art-video-player.art-playing video {
        visibility: visible !important;
        opacity: 1 !important;
    }
}

.player-container {
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
}

#player {
    width: 100%;
    height: 60vh; /* 视频播放器高度 */
}

.loading-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    z-index: 100;
    flex-direction: column;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 10px;
}

.error-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: none;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    z-index: 100;
    flex-direction: column;
    text-align: center;
    padding: 1rem;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.error-message-sub {
    margin-top: 10px;
    font-size: 14px;
    color: #aaa;
}

.episode-active {
    background-color: #3b82f6 !important;
    border-color: #60a5fa !important;
}

.episode-grid {
    max-height: 30vh;
    overflow-y: auto;
}

/* 恢复播放位置提示样式 */
.position-restore-hint {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(100%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    z-index: 1000;
    transition: transform 0.3s ease;
    font-size: 14px;
}

.position-restore-hint.show {
    transform: translateX(-50%) translateY(0);
}

.hint-content {
    display: flex;
    align-items: center;
    justify-content: center;
}

.switch {
    position: relative;
    display: inline-block;
    width: 46px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #333;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #00ccff;
}

input:checked + .slider:before {
    transform: translateX(22px);
}

/* 添加快捷键提示样式 */
.shortcut-hint {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.shortcut-hint.show {
    opacity: 1;
}

/* 原生全屏时，播放器容器铺满 */
.player-container:-webkit-full-screen,
.player-container:fullscreen {
    position: fixed;
    top: 0; left: 0;
    width: 100vw; height: 100vh;
    z-index: 10000;
    background-color: #000;
}

.player-container:-webkit-full-screen #player,
.player-container:fullscreen #player {
    width: 100%; height: 100%;
}

/* 资源信息卡片区 */
#resourceInfoBarContainer {
    align-items: center;
    border-radius: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.resource-info-bar-left {
    align-items: center;
    font-size: 1.1rem;
    font-weight: bold;
    color: #fff;
    flex: 1;
}

.resource-info-bar-videos {
    font-size: 1rem;
    font-weight: normal;
    margin-left: 10px;
    color: #ccc;
}

.resource-switch-btn {
    align-items: center;
    background: none;
    border: none;
    color: #a67c2d;
    font-weight: bold;
    font-size: 1rem;
    cursor: pointer;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 0.5rem;
    transition: background 0.2s;
}

.resource-switch-btn:hover {
    background: #f5e9d7;
}

.resource-switch-btn:active {
    background: #f5e9d7;
}

.resource-switch-icon {
    width: 20px;
    height: 20px;
    margin-right: 0;
    color: #a67c2d;
    vertical-align: middle;
    transition: transform 0.3s;
}

/* 新增：移动端响应式样式 */
@media (max-width: 640px) {
    .episode-grid {
        max-height: 40vh; /* 移动端增加集数列表高度 */
    }

    /* 改进移动端按钮显示 */
    button {
        white-space: nowrap;
    }

    /* 控制栏在小屏幕上可能需要换行 */
    .player-container .flex-wrap {
        margin-bottom: 4px;
    }
}

/* 隐藏用户名输入框 */
#username {
    display: none !important;
}
