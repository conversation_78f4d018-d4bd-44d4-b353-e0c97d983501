/* 
LibreTV 全局样式
包含多个页面共享的基础样式
对于特定页面的样式，请参考:
- index.css: 首页特定样式
- player.css: 播放器页面特定样式
- watch.css: 重定向页面特定样式
- modals.css: 模态框和提示框样式
*/

.close-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    background: #222;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 6px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 10;
}

.close-btn:hover {
    background: #333;
    border-color: #555;
}

.close-btn svg {
    width: 16px;
    height: 16px;
    stroke: currentColor;
}

:root {
    /* 默认主题配色方案 - 赛博朋克 */
    --primary: #00ccff;
    --primary-light: #33d6ff;
    --secondary: #0f1622;
    --accent: #ff3c78;
    --text: #e6f2ff;
    --text-muted: #8599b2;
    --border: rgba(0, 204, 255, 0.15);
    --page-gradient-start: #0f1622;
    --page-gradient-end: #192231;
    --card-gradient-start: #121b29;
    --card-gradient-end: #1c2939;
    --card-accent: rgba(0, 204, 255, 0.12);
    --card-hover-border: rgba(0, 204, 255, 0.5);
    --background-pattern: radial-gradient(circle at 25px 25px, rgba(0, 204, 255, 0.04) 2px, transparent 3px),
                         radial-gradient(circle at 75px 75px, rgba(255, 60, 120, 0.02) 1px, transparent 2px),
                         radial-gradient(circle at 50px 50px, rgba(150, 255, 250, 0.015) 1px, transparent 2px);

    /* 兼容旧变量名 */
    --primary-color: var(--primary);
    --primary-light: var(--primary-light);
    --secondary-color: var(--secondary);
    --accent-color: var(--accent);
    --text-color: var(--text);
    --text-muted: var(--text-muted);
    --border-color: var(--border);
}

.page-bg {
    background: linear-gradient(180deg, var(--page-gradient-start), var(--page-gradient-end));
    min-height: 100vh;
    /* 动态主题背景图案 */
    background-image:
        linear-gradient(180deg, var(--page-gradient-start), var(--page-gradient-end)),
        var(--background-pattern);
    background-blend-mode: normal;
    background-size: cover, 100px 100px, 50px 50px, 75px 75px;
    transition: background 0.3s ease;
}

button, .card-hover {
    transition: all 0.3s ease;
}

/* 改进卡片适应不同内容长度 */
.card-hover {
    border: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--card-gradient-start), var(--card-gradient-end));
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    position: relative;
    overflow: hidden;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* 确保卡片内容区域高度一致性 */
.card-hover .flex-grow {
    min-height: unset; /* 移除最小高度限制，让内容自然流动 */
    display: flex;
    flex-direction: column;
}

/* 针对不同长度的标题优化显示 */
.card-hover h3 {
    min-height: unset;
    max-height: unset;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    line-height: 1.2rem;
    word-break: break-word; /* 允许在任何字符间断行 */
    hyphens: auto; /* 允许断词 */
}

.card-hover::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--card-accent), transparent);
    transition: left 0.6s ease;
}

.card-hover:hover {
    border-color: var(--card-hover-border);
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.5);
}

.card-hover:hover::before {
    left: 100%;
}

.gradient-text {
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* 改进设置面板样式 */
.settings-panel {
    scrollbar-width: thin;
    scrollbar-color: #444 #222;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    background: linear-gradient(135deg, var(--page-gradient-end), var(--page-gradient-start));
    border-left: 1px solid var(--primary-color);
}

.settings-panel.show {
    transform: translateX(0);
}

.settings-panel::-webkit-scrollbar {
    width: 6px;
}

.settings-panel::-webkit-scrollbar-track {
    background: transparent;
}

.settings-panel::-webkit-scrollbar-thumb {
    background-color: #444;
    border-radius: 4px;
}

.search-button {
    background: var(--primary);
    color: var(--text);
    transition: all 0.3s ease;
}

.search-button:hover {
    background: var(--primary-light);
}

::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #111;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 4px;
    transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: #444;
}

* {
    scrollbar-width: thin;
    scrollbar-color: #333 #111;
}

.search-tag {
    background: linear-gradient(135deg, var(--card-gradient-start), var(--card-gradient-end));
    color: var(--text-color);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.search-tag:hover {
    background: linear-gradient(135deg, var(--card-gradient-end), var(--card-gradient-start));
    border-color: var(--primary-color);
}

.footer {
    width: 100%;
    transition: all 0.3s ease;
    margin-top: auto;
    background: linear-gradient(to bottom, transparent, var(--page-gradient-start));
    border-top: 1px solid var(--border-color);
}

.footer a:hover {
    text-decoration: underline;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.container {
    flex: 1;
}

@media screen and (min-height: 800px) {
    body {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
    }
    
    .container {
        flex: 1;
    }
    
    .footer {
        margin-top: auto;
    }
}

@media screen and (max-width: 640px) {
    .footer {
        padding-bottom: 2rem;
    }
}

/* 移动端布局优化 */
@media screen and (max-width: 768px) {
    .card-hover h3 {
        min-height: 2.5rem;
    }
    
    .card-hover .flex-grow {
        min-height: 80px;
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

#modal.show {
    animation: fadeIn 0.3s forwards;
}

#modal.hide {
    animation: fadeOut 0.3s forwards;
}

#modal > div {
    background: linear-gradient(135deg, var(--card-gradient-start), var(--card-gradient-end));
    border: 1px solid var(--primary);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.7), 0 0 15px var(--card-accent);
    border-radius: 8px;
    transition: all 0.3s ease;
}

#episodesGrid button {
    background: rgba(0, 204, 255, 0.08);
    border: 1px solid rgba(0, 204, 255, 0.2);
    transition: all 0.2s ease;
}

#episodesGrid button:hover {
    background: rgba(0, 204, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 8px rgba(0, 204, 255, 0.3);
}

#yellowFilterToggle:checked + .toggle-bg {
    background-color: var(--primary);
}

#yellowFilterToggle:checked ~ .toggle-dot {
    transform: translateX(1.5rem);
}

#yellowFilterToggle:focus + .toggle-bg,
#yellowFilterToggle:hover + .toggle-bg {
    box-shadow: 0 0 0 2px var(--card-accent);
}

/* 添加广告过滤开关的CSS */
#adFilterToggle:checked + .toggle-bg {
    background-color: var(--primary);
}

#adFilterToggle:checked ~ .toggle-dot {
    transform: translateX(1.5rem);
}

#adFilterToggle:focus + .toggle-bg,
#adFilterToggle:hover + .toggle-bg {
    box-shadow: 0 0 0 2px var(--card-accent);
}

.toggle-dot {
    transition: transform 0.3s ease-in-out;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.toggle-bg {
    transition: background-color 0.3s ease-in-out;
}

#yellowFilterToggle:checked ~ .toggle-dot {
    box-shadow: 0 2px 4px rgba(0, 204, 255, 0.3);
}

#adFilterToggle:checked ~ .toggle-dot {
    box-shadow: 0 2px 4px rgba(0, 204, 255, 0.3);
}

/* 添加API复选框样式 */
.form-checkbox {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    height: 14px;
    width: 14px;
    background-color: #222;
    border: 1px solid #333;
    border-radius: 3px;
    cursor: pointer;
    position: relative;
    outline: none;
}

.form-checkbox:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-checkbox:checked::after {
    content: '';
    position: absolute;
    left: 4px;
    top: 1px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* API滚动区域美化 */
#apiCheckboxes {
    scrollbar-width: thin;
    scrollbar-color: #444 #222;
}

#apiCheckboxes::-webkit-scrollbar {
    width: 6px;
}

#apiCheckboxes::-webkit-scrollbar-track {
    background: #222;
    border-radius: 4px;
}

#apiCheckboxes::-webkit-scrollbar-thumb {
    background-color: #444;
    border-radius: 4px;
}

/* 自定义API列表样式 */
#customApisList {
    scrollbar-width: thin;
    scrollbar-color: #444 #222;
}

#customApisList::-webkit-scrollbar {
    width: 6px;
}

#customApisList::-webkit-scrollbar-track {
    background: transparent;
}

#customApisList::-webkit-scrollbar-thumb {
    background-color: #444;
    border-radius: 4px;
}

/* 设置面板滚动样式 */
.settings-panel {
    scrollbar-width: thin;
    scrollbar-color: #444 #222;
}

.settings-panel::-webkit-scrollbar {
    width: 6px;
}

.settings-panel::-webkit-scrollbar-track {
    background: transparent;
}

.settings-panel::-webkit-scrollbar-thumb {
    background-color: #444;
    border-radius: 4px;
}

/* 添加自定义API表单动画 */
#addCustomApiForm {
    transition: all 0.3s ease;
    max-height: 0;
    opacity: 0;
    overflow: hidden;
}

#addCustomApiForm.hidden {
    max-height: 0;
    padding: 0;
    opacity: 0;
}

#addCustomApiForm:not(.hidden) {
    max-height: 230px;
    opacity: 1;
}

/* 成人内容API标记样式 */
.api-adult + label {
    color: #ff6b8b !important;
}

/* 添加警告图标和标签样式 */
.adult-warning {
    display: inline-flex;
    align-items: center;
    margin-left: 0.25rem;
    color: #ff6b8b;
}

.adult-warning svg {
    width: 12px;
    height: 12px;
    margin-right: 4px;
}

/* 过滤器禁用样式 */
.filter-disabled {
    opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
}

/* API组标题样式 */
.api-group-title {
    grid-column: span 2;
    padding: 0.25rem 0;
    margin-top: 0.5rem;
    border-top: 1px solid #333;
    color: #8599b2;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.api-group-title.adult {
    color: #ff6b8b;
}

/* 过滤器禁用样式 - 改进版本 */
.filter-disabled {
    position: relative;
}

.filter-disabled::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.4);
    border-radius: 0.5rem;
    z-index: 5;
}

.filter-disabled > * {
    opacity: 0.7;
}

.filter-disabled .toggle-bg {
    background-color: #333 !important;
}

.filter-disabled .toggle-dot {
    transform: translateX(0) !important;
    background-color: #666 !important;
}

/* 改进过滤器禁用样式 */
.filter-disabled .filter-description {
    color: #ff6b8b !important;
    font-style: italic;
    font-weight: 500;
}

/* 修改过滤器禁用样式，确保文字清晰可见 */
.filter-disabled {
    position: relative;
}

.filter-disabled::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.3);
    border-radius: 0.5rem;
    z-index: 5;
}

.filter-disabled > * {
    opacity: 1; /* 提高子元素不透明度，保证可见性 */
    z-index: 6; /* 确保内容在遮罩上方 */
}

/* 改进过滤器禁用状态下的描述样式 */
.filter-disabled .filter-description {
    color: #ff7b9d !important; /* 更亮的粉色 */
    font-style: italic;
    font-weight: 500;
    text-shadow: 0 0 2px rgba(0,0,0,0.8); /* 添加文字阴影提高对比度 */
}

/* 开关的禁用样式 */
.filter-disabled .toggle-bg {
    background-color: #444 !important;
    opacity: 0.8;
}

.filter-disabled .toggle-dot {
    transform: translateX(0) ;
    background-color: #777 ;
    opacity: 0.9;
}

/* 警告提示样式改进 */
.filter-tooltip {
    background-color: rgba(255, 61, 87, 0.1);
    border: 1px solid rgba(255, 61, 87, 0.2);
    border-radius: 0.25rem;
    padding: 0.5rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    line-height: 1.25;
    position: relative;
    z-index: 10;
}

.filter-tooltip svg {
    flex-shrink: 0;
    width: 14px;
    height: 14px;
    margin-right: 0.35rem;
}

/* 编辑按钮样式 */
.custom-api-edit {
    color: #3b82f6;
    transition: color 0.2s ease;
}

.custom-api-edit:hover {
    color: #2563eb;
}

/* 自定义API条目样式改进 */
#customApisList .api-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.25rem 0.5rem;
    margin-bottom: 0.25rem;
    background-color: #222;
    border-radius: 0.25rem;
    transition: background-color 0.2s ease;
}

#customApisList .api-item:hover {
    background-color: #2a2a2a;
}

/* 成人内容标签样式 */
.adult-tag {
    display: inline-flex;
    align-items: center;
    color: #ff6b8b;
    font-size: 0.7rem;
    font-weight: 500;
    margin-right: 0.35rem;
}

/* 历史记录面板样式 */
.history-panel {
    box-shadow: 2px 0 10px rgba(0,0,0,0.5);
    transition: transform 0.3s ease-in-out;
    overflow-y: scroll; /* 始终显示滚动条，防止宽度变化 */
    overflow-x: hidden; /* 防止水平滚动 */
    width: 320px; /* 固定宽度 */
    box-sizing: border-box; /* 确保padding不影响总宽度 */
    scrollbar-gutter: stable; /* 现代浏览器：为滚动条预留空间 */
}

.history-panel.show {
    transform: translateX(0);
}

#historyList {
    padding-right: 6px; /* 为滚动条预留空间，确保内容不被挤压 */
}

/* 历史记录项样式优化 */
.history-item {
    background: #1a1a1a;
    border-radius: 6px; /* 减小圆角 */
    border: 1px solid #333;
    overflow: hidden;
    transition: all 0.2s ease;
    padding: 10px 14px;
    position: relative;
    margin-bottom: 8px; /* 减小底部间距 */
    width: 100%; /* 确保宽度一致 */
}

.history-item:hover {
    transform: translateY(-2px);
    border-color: #444;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* 添加组悬停效果，使删除按钮在悬停时显示 */
.history-item .delete-btn {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.history-item:hover .delete-btn {
    opacity: 1;
}

.history-info {
    padding: 0; /* 移除额外的内边距 */
    min-height: 70px;
}

.history-title {
    font-weight: 500;
    font-size: 0.95rem; /* 减小字体大小 */
    margin-bottom: 2px; /* 减小底部边距 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.history-meta {
    color: #bbb;
    font-size: 0.75rem; /* 减小字体大小 */
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 1px; /* 减小边距 */
}

.history-episode {
    color: #3b82f6;
}

.history-source {
    color: #10b981;
}

.history-time {
    color: #888;
    font-size: 0.7rem; /* 减小字体大小 */
    margin-top: 1px; /* 减小顶部边距 */
}

.history-separator {
    color: #666;
}

.history-thumbnail {
    width: 100%;
    height: 90px;
    background-color: #222;
    overflow: hidden;
}

.history-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.history-info {
    padding: 10px;
}

.history-time {
    color: #888;
    font-size: 0.8rem;
    margin-top: 4px;
}

.history-title {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 添加播放进度条样式 */
.history-progress {
    margin: 5px 0;
}

.progress-bar {
    height: 3px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 2px;
}

.progress-filled {
    height: 100%;
    background: linear-gradient(to right, #00ccff, #3b82f6);
    border-radius: 2px;
}

.progress-text {
    font-size: 10px;
    color: #888;
    text-align: right;
}

/* 添加恢复播放提示样式 */
.position-restore-hint {
    position: absolute;
    bottom: 60px;
    left: 50%;
    transform: translateX(-50%) translateY(20px);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 100;
    opacity: 0;
    transition: all 0.3s ease;
}

.position-restore-hint.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

/* 锁定控制时屏蔽交互 */
.player-container.controls-locked .dplayer-controller,
.player-container.controls-locked .dplayer-mask,
.player-container.controls-locked .dplayer-bar-wrap,
.player-container.controls-locked .dplayer-statusbar,
.player-container.controls-locked .shortcut-hint {
    opacity: 0 !important;
    pointer-events: none !important;
}
/* 保留锁按钮可见可点 */
.player-container.controls-locked #lockToggle {
    opacity: 1 !important;
    pointer-events: auto !important;
}

/* 播放器顶部header移动端优化 */
.player-header {
    gap: 0.5rem;
}
.custom-title-scroll {
    overflow-x: auto;
    white-space: nowrap;
    text-overflow: ellipsis;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
}
.custom-title-scroll::-webkit-scrollbar {
    display: none;
}
.logo-text {
    display: inline;
}
.home-btn-text {
    display: inline;
}
@media (max-width: 640px) {
    .logo-text {
        display: none;
    }
    .home-btn-text {
        display: none;
    }
    .logo-icon {
        margin-right: 0;
    }
    .home-btn svg {
        margin-right: 0;
    }
    .player-header {
        padding-left: 2px !important;
        padding-right: 2px !important;
    }
    .custom-title-scroll {
        font-size: 1rem;
    }
}

/* 搜索结果卡片优化：横向布局 */
.search-card-img-container {
    width: 100px;  /* 增加宽度，从80px到100px */
    height: 150px; /* 增加高度，从120px到150px */
    overflow: hidden;
    background-color: #191919;
}

/* 确保图片不会被拉伸，并且能够正确显示 */
.search-card-img-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 针对搜索结果卡片修改网格布局以适应横向卡片 */
@media (max-width: 640px) {
    #results {
        grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
    }
}

/* 响应式调整：在小屏幕上依然保持较好的视觉效果 */
@media (min-width: 641px) and (max-width: 768px) {
    #results {
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }
}

/* 调整网格布局，减少每行卡片数量以适应更大尺寸的卡片 */
@media (min-width: 769px) and (max-width: 1024px) {
    #results {
        grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
    }
}

@media (min-width: 1025px) {
    #results {
        grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
    }
}

/* 优化卡片内元素间距 */
.card-hover .p-2 {
    padding: 0.75rem; /* 增加内边距 */
}

/* 增加卡片内字体大小 */
.card-hover h3 {
    font-size: 0.95rem; /* 增加标题字体大小 */
    line-height: 1.3rem;
    margin-bottom: 0.5rem;
}

.card-hover p {
    font-size: 0.8rem; /* 增加描述字体大小 */
}

/* 优化卡片内元素间距 */
.card-hover .p-2 {
    padding: 0.5rem;
}

/* 确保Toast显示在顶层并有适当的转换效果 */
#toast {
    z-index: 9999; /* 确保显示在最上层 */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    min-width: 200px;
    text-align: center;
    pointer-events: none; /* 防止toast阻挡点击事件 */
    transform: translateX(-50%) translateY(0);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

#toast.hidden {
    opacity: 0;
    transform: translateX(-50%) translateY(-100%);
}

/* 详情模态框样式优化 */
#modal .modal-detail-info {
    background: linear-gradient(135deg, #0a0a0a, #111);
    border: 1px solid #222;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

#modal .detail-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
    font-size: 0.875rem;
}

@media (min-width: 768px) {
    #modal .detail-grid {
        grid-template-columns: 1fr 1fr;
    }
}

#modal .detail-item {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

#modal .detail-label {
    color: #9ca3af;
    font-weight: 500;
    min-width: 3rem;
    flex-shrink: 0;
}

#modal .detail-value {
    color: white;
    flex: 1;
    word-break: break-word;
}

#modal .detail-desc {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #333;
}

#modal .detail-desc-content {
    color: #d1d5db;
    font-size: 0.875rem;
    line-height: 1.6;
    max-height: 8rem;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #444 #222;
}

#modal .detail-desc-content::-webkit-scrollbar {
    width: 6px;
}

#modal .detail-desc-content::-webkit-scrollbar-track {
    background: #222;
    border-radius: 4px;
}

#modal .detail-desc-content::-webkit-scrollbar-thumb {
    background-color: #444;
    border-radius: 4px;
}

/* 集数统计信息样式 */
#modal .episode-stats {
    color: #9ca3af;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 移动端优化 */
@media (max-width: 640px) {
    #modal .detail-grid {
        gap: 0.5rem;
        font-size: 0.8rem;
    }
    
    #modal .detail-label {
        min-width: 2.5rem;
    }
    
    #modal .detail-desc-content {
        max-height: 6rem;
        font-size: 0.8rem;
    }
}
