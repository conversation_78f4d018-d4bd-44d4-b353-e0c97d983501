name: Upstream Sync

permissions:
  contents: write

on:
  schedule:
    - cron: "0 4 * * *" # At 12PM UTC+8
  workflow_dispatch:

jobs:
  sync_latest_from_upstream:
    name: Sync latest commits from upstream repo
    runs-on: ubuntu-latest
    if: ${{ github.event.repository.fork }}

    steps:
      # Step 1: run a standard checkout action
      - name: Checkout target repo
        uses: actions/checkout@v4

      # Step 2: Sync upstream changes
      - name: Sync upstream changes
        id: sync
        uses: aormsby/Fork-Sync-With-Upstream-action@v3.4.1
        with:
          upstream_sync_repo: LibreSpark/LibreTV
          upstream_sync_branch: main
          target_sync_branch: main
          target_repo_token: ${{ secrets.GITHUB_TOKEN }}

      # Step 3: Reset changes in .github/workflows (IMPORTANT: Must run *after* sync)
      - name: Reset Workflow Changes
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "GitHub Actions"
          git checkout -- .github/workflows/  # Discard local changes in .github/workflows
          git commit -m "Revert Workflow changes from upstream" || true # Commit the revert, but allow it to fail if there are no changes

      - name: Push changes (excluding workflows)
        run: |
          git push origin HEAD:${{ github.ref }} # Push the changes, excluding the rebased Workflow directory

      - name: Sync check
        if: failure()
        run: |
          echo "[Warning] Upstream sync failed (likely due to workflow file). Manual sync recommended."
          exit 1 # Or just a warning and don't exit.
