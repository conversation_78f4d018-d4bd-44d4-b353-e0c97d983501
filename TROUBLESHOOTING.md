# LibreTV 故障排除指南

## 🚨 搜索功能问题

如果您遇到搜索错误或控制台报错，请按以下步骤排查：

### 1. 快速诊断

**打开浏览器开发者工具**:
- Chrome/Edge: 按 `F12` 或 `Ctrl+Shift+I`
- Firefox: 按 `F12` 或 `Ctrl+Shift+I`
- Safari: 按 `Cmd+Option+I`

**检查控制台错误**:
- 查看是否有红色错误信息
- 记录具体的错误消息

### 2. 使用内置诊断工具

**方法1: 调试面板**
- 按 `Ctrl+Shift+D` 打开调试面板
- 点击"检查系统状态"
- 查看各项功能是否正常

**方法2: 简单测试页面**
- 访问 `simple-test.html` 页面
- 运行各项测试检查功能状态

### 3. 常见问题及解决方案

#### 问题1: AbortSignal 超时错误
```
Failed to execute 'timeout' on 'AbortSignal': Value is not of type 'unsigned long long'
```

**解决方案**:
- 已在最新版本中修复
- 如仍有问题，请清除浏览器缓存后重试

#### 问题2: 所有API搜索失败
```
API 搜索失败: ReferenceError: data is not defined
```

**解决方案**:
1. 检查网络连接
2. 尝试更换数据源
3. 检查代理设置是否正确

#### 问题3: 自定义API不工作

**解决方案**:
1. 确保 `_routes.json` 文件已正确部署
2. 检查API URL格式是否正确
3. 验证API返回的数据格式

### 4. 手动修复步骤

#### 清除缓存和数据
```javascript
// 在浏览器控制台中执行
localStorage.clear();
sessionStorage.clear();
location.reload();
```

#### 重置API配置
```javascript
// 在浏览器控制台中执行
localStorage.removeItem('selectedAPIs');
localStorage.removeItem('customAPIs');
location.reload();
```

#### 启用备用搜索模式
```javascript
// 在浏览器控制台中执行
window.emergencySearch('测试').then(results => {
    console.log('紧急搜索结果:', results);
});
```

### 5. 浏览器兼容性

**推荐浏览器版本**:
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

**不支持的浏览器**:
- Internet Explorer (所有版本)
- Chrome 60 以下版本
- Firefox 60 以下版本

### 6. 网络环境检查

#### 检查代理设置
1. 确认 `/proxy/` 路径可访问
2. 测试代理是否正常工作:
   ```
   访问: /proxy/https%3A//httpbin.org/json
   ```

#### 检查CORS设置
- 确保服务器正确配置了CORS头
- 检查是否有安全策略阻止请求

### 7. Cloudflare Pages 特殊问题

#### 路由配置问题
确保 `_routes.json` 文件内容正确:
```json
{
  "version": 1,
  "include": ["/proxy/*"],
  "exclude": ["/css/*", "/js/*", "/libs/*", "/image/*", "/*.html"]
}
```

#### 函数部署问题
检查 `functions/proxy/[[path]].js` 是否正确部署

### 8. 紧急备用方案

如果主搜索功能完全不可用，可以使用以下备用方案:

#### 方案1: 使用备用搜索
```javascript
// 在控制台中执行
window.fallbackBatchSearch(['ikun', 'wwzy'], '搜索关键词')
  .then(results => console.log('备用搜索结果:', results));
```

#### 方案2: 直接API调用
```javascript
// 在控制台中执行
fetch('/proxy/' + encodeURIComponent('API_URL_HERE'))
  .then(r => r.json())
  .then(data => console.log('API数据:', data));
```

### 9. 获取帮助

#### 导出调试信息
1. 按 `Ctrl+Shift+D` 打开调试面板
2. 点击"导出日志"
3. 将导出的日志文件发送给技术支持

#### 提供问题报告时请包含:
- 浏览器版本和操作系统
- 具体的错误消息
- 重现问题的步骤
- 控制台截图
- 导出的调试日志

### 10. 预防措施

#### 定期维护
- 定期清理浏览器缓存
- 更新到最新版本
- 检查API源的可用性

#### 最佳实践
- 选择多个可靠的API源
- 避免使用不稳定的自定义API
- 定期备份重要设置

---

## 📞 技术支持

如果以上方法都无法解决问题，请:
1. 收集完整的错误信息
2. 导出调试日志
3. 提供详细的问题描述
4. 联系技术支持

**记住**: 大多数搜索问题都是由网络连接或API源不可用导致的，请首先检查这些基础问题。
