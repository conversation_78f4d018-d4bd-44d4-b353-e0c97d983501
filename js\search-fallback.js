// 备用搜索实现 - 不使用 AbortController
// 如果主搜索功能有问题，可以使用这个备用版本

// 简单的超时Promise包装器
function fetchWithTimeout(url, options = {}, timeoutMs = 10000) {
    return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
            reject(new Error('请求超时'));
        }, timeoutMs);

        fetch(url, options)
            .then(response => {
                clearTimeout(timeoutId);
                resolve(response);
            })
            .catch(error => {
                clearTimeout(timeoutId);
                reject(error);
            });
    });
}

// 备用搜索函数
async function fallbackSearchByAPI(apiId, query) {
    console.log(`使用备用搜索: ${apiId}, 关键词: ${query}`);
    
    try {
        let apiUrl, apiName;
        
        // 获取API配置
        if (apiId.startsWith('custom_')) {
            const customIndex = apiId.replace('custom_', '');
            const customAPIs = JSON.parse(localStorage.getItem('customAPIs') || '[]');
            const customApi = customAPIs[parseInt(customIndex)];
            
            if (!customApi) {
                console.warn(`自定义API ${apiId} 不存在`);
                return [];
            }
            
            apiUrl = customApi.url;
            apiName = customApi.name;
        } else {
            if (!window.API_SITES || !window.API_SITES[apiId]) {
                console.warn(`内置API ${apiId} 不存在`);
                return [];
            }
            
            apiUrl = window.API_SITES[apiId].api;
            apiName = window.API_SITES[apiId].name;
        }
        
        // 构建搜索URL
        const searchPath = window.API_CONFIG?.search?.path || '?ac=videolist&wd=';
        const fullApiUrl = apiUrl + searchPath + encodeURIComponent(query);
        const proxyUrl = (window.PROXY_URL || '/proxy/') + encodeURIComponent(fullApiUrl);
        
        console.log(`备用搜索请求: ${proxyUrl}`);
        
        // 发起请求
        const response = await fetchWithTimeout(proxyUrl, {
            headers: window.API_CONFIG?.search?.headers || {}
        }, 8000);
        
        if (!response.ok) {
            console.warn(`API ${apiId} 返回错误状态: ${response.status}`);
            return [];
        }
        
        const responseText = await response.text();
        
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.warn(`API ${apiId} JSON解析失败:`, parseError.message);
            return [];
        }
        
        if (!data || !data.list || !Array.isArray(data.list)) {
            console.warn(`API ${apiId} 数据格式无效`);
            return [];
        }
        
        // 处理结果
        const results = data.list.map(item => ({
            ...item,
            source_name: apiName,
            source_code: apiId,
            api_url: apiId.startsWith('custom_') ? apiUrl : undefined
        }));
        
        console.log(`API ${apiId} 返回 ${results.length} 条结果`);
        return results;
        
    } catch (error) {
        console.error(`备用搜索失败 ${apiId}:`, error.message);
        return [];
    }
}

// 备用批量搜索函数
async function fallbackBatchSearch(selectedAPIs, query) {
    console.log('使用备用批量搜索');
    
    const searchPromises = selectedAPIs.map(apiId => 
        fallbackSearchByAPI(apiId, query)
    );
    
    try {
        // 使用 Promise.allSettled 确保部分失败不影响其他结果
        const results = await Promise.allSettled(searchPromises);
        
        let allResults = [];
        results.forEach((result, index) => {
            if (result.status === 'fulfilled' && Array.isArray(result.value)) {
                allResults = allResults.concat(result.value);
            } else if (result.status === 'rejected') {
                console.warn(`API ${selectedAPIs[index]} 搜索失败:`, result.reason);
            }
        });
        
        return allResults;
        
    } catch (error) {
        console.error('备用批量搜索失败:', error);
        return [];
    }
}

// 简化的搜索函数，供紧急使用
async function emergencySearch(query) {
    console.log('使用紧急搜索模式');
    
    if (!query || !query.trim()) {
        return [];
    }
    
    // 使用最基本的API测试
    const testApis = ['ikun', 'wwzy']; // 使用相对稳定的API
    const results = [];
    
    for (const apiId of testApis) {
        try {
            const apiResults = await fallbackSearchByAPI(apiId, query.trim());
            if (apiResults && apiResults.length > 0) {
                results.push(...apiResults);
                // 如果已经有结果，可以提前返回
                if (results.length >= 10) {
                    break;
                }
            }
        } catch (error) {
            console.warn(`紧急搜索 ${apiId} 失败:`, error);
            continue;
        }
    }
    
    return results;
}

// 检查搜索功能是否可用
function checkSearchAvailability() {
    const checks = {
        fetch: typeof fetch === 'function',
        promise: typeof Promise === 'function',
        json: typeof JSON === 'object' && typeof JSON.parse === 'function',
        localStorage: (() => {
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                return true;
            } catch (e) {
                return false;
            }
        })(),
        network: navigator.onLine
    };
    
    const available = Object.values(checks).every(check => check);
    
    console.log('搜索功能可用性检查:', checks, '总体可用:', available);
    
    return { checks, available };
}

// 导出函数供全局使用
window.fallbackSearchByAPI = fallbackSearchByAPI;
window.fallbackBatchSearch = fallbackBatchSearch;
window.emergencySearch = emergencySearch;
window.checkSearchAvailability = checkSearchAvailability;
window.fetchWithTimeout = fetchWithTimeout;

console.log('备用搜索模块已加载');
