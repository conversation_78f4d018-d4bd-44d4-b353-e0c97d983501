// 调试工具函数
class DebugHelper {
    constructor() {
        this.logs = [];
        this.maxLogs = 100;
    }

    log(level, message, data = null) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level,
            message,
            data
        };
        
        this.logs.push(logEntry);
        
        // 保持日志数量在限制内
        if (this.logs.length > this.maxLogs) {
            this.logs = this.logs.slice(-this.maxLogs);
        }
        
        // 输出到控制台
        const consoleMethod = level === 'error' ? 'error' : 
                             level === 'warn' ? 'warn' : 'log';
        console[consoleMethod](`[${timestamp}] ${message}`, data || '');
    }

    error(message, data) {
        this.log('error', message, data);
    }

    warn(message, data) {
        this.log('warn', message, data);
    }

    info(message, data) {
        this.log('info', message, data);
    }

    // 获取最近的日志
    getRecentLogs(count = 20) {
        return this.logs.slice(-count);
    }

    // 清空日志
    clearLogs() {
        this.logs = [];
    }

    // 导出日志
    exportLogs() {
        const logText = this.logs.map(log => 
            `[${log.timestamp}] [${log.level.toUpperCase()}] ${log.message}${log.data ? ' | Data: ' + JSON.stringify(log.data) : ''}`
        ).join('\n');
        
        const blob = new Blob([logText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `libretv-debug-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // 检查系统状态
    checkSystemStatus() {
        const status = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            online: navigator.onLine,
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            localStorage: this.checkLocalStorage(),
            apis: this.checkAPIs(),
            themes: this.checkThemes(),
            proxy: this.checkProxy()
        };
        
        this.info('系统状态检查', status);
        return status;
    }

    checkLocalStorage() {
        try {
            const testKey = 'debug_test';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            return { available: true, error: null };
        } catch (e) {
            return { available: false, error: e.message };
        }
    }

    checkAPIs() {
        const selectedAPIs = JSON.parse(localStorage.getItem('selectedAPIs') || '[]');
        const customAPIs = JSON.parse(localStorage.getItem('customAPIs') || '[]');
        
        return {
            selected: selectedAPIs,
            selectedCount: selectedAPIs.length,
            custom: customAPIs,
            customCount: customAPIs.length,
            builtin: Object.keys(window.API_SITES || {}),
            builtinCount: Object.keys(window.API_SITES || {}).length
        };
    }

    checkThemes() {
        const currentTheme = localStorage.getItem('selectedTheme') || 'cyber';
        const availableThemes = Object.keys(window.THEMES || {});
        
        return {
            current: currentTheme,
            available: availableThemes,
            themeManagerLoaded: !!window.themeManager
        };
    }

    checkProxy() {
        return {
            proxyUrl: window.PROXY_URL || '/proxy/',
            configLoaded: !!window.API_CONFIG
        };
    }

    // 测试API连接
    async testAPIConnection(apiId) {
        this.info(`开始测试API连接: ${apiId}`);
        
        try {
            let apiUrl, apiName;
            
            if (apiId.startsWith('custom_')) {
                const customIndex = apiId.replace('custom_', '');
                const customAPIs = JSON.parse(localStorage.getItem('customAPIs') || '[]');
                const customApi = customAPIs[parseInt(customIndex)];
                
                if (!customApi) {
                    throw new Error('自定义API不存在');
                }
                
                apiUrl = customApi.url;
                apiName = customApi.name;
            } else {
                if (!window.API_SITES || !window.API_SITES[apiId]) {
                    throw new Error('内置API不存在');
                }
                
                apiUrl = window.API_SITES[apiId].api;
                apiName = window.API_SITES[apiId].name;
            }
            
            const testQuery = '测试';
            const fullUrl = apiUrl + (window.API_CONFIG?.search?.path || '?ac=videolist&wd=') + encodeURIComponent(testQuery);
            const proxyUrl = (window.PROXY_URL || '/proxy/') + encodeURIComponent(fullUrl);
            
            this.info(`测试URL: ${proxyUrl}`);
            
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000);
            
            const startTime = Date.now();
            const response = await fetch(proxyUrl, {
                headers: window.API_CONFIG?.search?.headers || {},
                signal: controller.signal
            });
            const endTime = Date.now();
            
            clearTimeout(timeoutId);
            
            const result = {
                apiId,
                apiName,
                success: response.ok,
                status: response.status,
                statusText: response.statusText,
                responseTime: endTime - startTime,
                contentType: response.headers.get('content-type')
            };
            
            if (response.ok) {
                try {
                    const data = await response.json();
                    result.hasData = !!(data && data.list && Array.isArray(data.list));
                    result.resultCount = data.list ? data.list.length : 0;
                } catch (e) {
                    result.jsonError = e.message;
                }
            }
            
            this.info(`API测试完成: ${apiId}`, result);
            return result;
            
        } catch (error) {
            const result = {
                apiId,
                success: false,
                error: error.message,
                errorType: error.name
            };
            
            this.error(`API测试失败: ${apiId}`, result);
            return result;
        }
    }

    // 显示调试面板
    showDebugPanel() {
        // 移除现有面板
        const existingPanel = document.getElementById('debugPanel');
        if (existingPanel) {
            existingPanel.remove();
        }

        const panel = document.createElement('div');
        panel.id = 'debugPanel';
        panel.className = 'fixed top-4 right-4 w-96 max-h-96 bg-black bg-opacity-90 text-white p-4 rounded-lg border border-gray-600 z-50 overflow-y-auto';
        panel.innerHTML = `
            <div class="flex justify-between items-center mb-3">
                <h3 class="text-lg font-bold">调试面板</h3>
                <button onclick="window.debugHelper.hideDebugPanel()" class="text-gray-400 hover:text-white">&times;</button>
            </div>
            <div class="space-y-2 text-sm">
                <button onclick="window.debugHelper.checkSystemStatus()" class="w-full bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded">检查系统状态</button>
                <button onclick="window.debugHelper.exportLogs()" class="w-full bg-green-600 hover:bg-green-700 px-3 py-1 rounded">导出日志</button>
                <button onclick="window.debugHelper.clearLogs()" class="w-full bg-red-600 hover:bg-red-700 px-3 py-1 rounded">清空日志</button>
            </div>
            <div class="mt-3">
                <h4 class="font-semibold mb-2">最近日志:</h4>
                <div id="debugLogs" class="text-xs space-y-1 max-h-32 overflow-y-auto">
                    ${this.getRecentLogs(10).map(log => 
                        `<div class="text-gray-300">[${log.level}] ${log.message}</div>`
                    ).join('')}
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
    }

    hideDebugPanel() {
        const panel = document.getElementById('debugPanel');
        if (panel) {
            panel.remove();
        }
    }
}

// 创建全局调试实例
window.debugHelper = new DebugHelper();

// 添加快捷键支持 (Ctrl+Shift+D)
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        e.preventDefault();
        window.debugHelper.showDebugPanel();
    }
});

// 监听未捕获的错误
window.addEventListener('error', function(e) {
    window.debugHelper.error('未捕获的错误', {
        message: e.message,
        filename: e.filename,
        lineno: e.lineno,
        colno: e.colno,
        stack: e.error?.stack
    });
});

// 监听未处理的Promise拒绝
window.addEventListener('unhandledrejection', function(e) {
    window.debugHelper.error('未处理的Promise拒绝', {
        reason: e.reason,
        stack: e.reason?.stack
    });
});
