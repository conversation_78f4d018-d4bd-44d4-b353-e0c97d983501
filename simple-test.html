<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单搜索测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        input, button {
            padding: 10px;
            margin: 5px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #333;
            color: #fff;
        }
        button {
            background: #0066cc;
            cursor: pointer;
        }
        button:hover {
            background: #0088ff;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #333;
            border-radius: 4px;
        }
        .error {
            color: #ff6666;
        }
        .success {
            color: #66ff66;
        }
        .warning {
            color: #ffaa00;
        }
    </style>
</head>
<body>
    <h1>LibreTV 简单搜索测试</h1>
    
    <div class="test-section">
        <h2>基础功能测试</h2>
        <button onclick="testBasicFunctions()">测试基础功能</button>
        <div id="basicResults"></div>
    </div>
    
    <div class="test-section">
        <h2>网络连接测试</h2>
        <button onclick="testNetworkConnection()">测试网络连接</button>
        <div id="networkResults"></div>
    </div>
    
    <div class="test-section">
        <h2>简单搜索测试</h2>
        <input type="text" id="searchInput" placeholder="输入搜索关键词" value="复仇者">
        <button onclick="testSimpleSearch()">开始搜索</button>
        <div id="searchResults"></div>
    </div>
    
    <div class="test-section">
        <h2>API配置测试</h2>
        <button onclick="testAPIConfig()">检查API配置</button>
        <div id="apiResults"></div>
    </div>

    <script>
        // 基础功能测试
        function testBasicFunctions() {
            const results = document.getElementById('basicResults');
            results.innerHTML = '';
            
            const tests = [
                {
                    name: 'fetch API支持',
                    test: () => typeof fetch === 'function',
                    fix: 'fetch API不支持，请使用现代浏览器'
                },
                {
                    name: 'Promise支持',
                    test: () => typeof Promise === 'function',
                    fix: 'Promise不支持，请使用现代浏览器'
                },
                {
                    name: 'AbortController支持',
                    test: () => typeof AbortController === 'function',
                    fix: 'AbortController不支持，将使用备用方案'
                },
                {
                    name: 'localStorage支持',
                    test: () => {
                        try {
                            localStorage.setItem('test', 'test');
                            localStorage.removeItem('test');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    },
                    fix: 'localStorage不可用，设置无法保存'
                },
                {
                    name: '网络连接状态',
                    test: () => navigator.onLine,
                    fix: '网络连接异常'
                }
            ];
            
            tests.forEach(test => {
                const result = test.test();
                const div = document.createElement('div');
                div.className = 'result ' + (result ? 'success' : 'error');
                div.innerHTML = `${test.name}: ${result ? '✓ 正常' : '✗ ' + test.fix}`;
                results.appendChild(div);
            });
        }
        
        // 网络连接测试
        async function testNetworkConnection() {
            const results = document.getElementById('networkResults');
            results.innerHTML = '<div class="result">正在测试网络连接...</div>';
            
            try {
                // 测试基本网络连接
                const response = await fetch('/', { 
                    method: 'HEAD',
                    cache: 'no-cache'
                });
                
                if (response.ok) {
                    results.innerHTML = '<div class="result success">✓ 网络连接正常</div>';
                } else {
                    results.innerHTML = '<div class="result warning">⚠ 网络连接异常，状态码: ' + response.status + '</div>';
                }
            } catch (error) {
                results.innerHTML = '<div class="result error">✗ 网络连接失败: ' + error.message + '</div>';
            }
        }
        
        // 简单搜索测试
        async function testSimpleSearch() {
            const input = document.getElementById('searchInput');
            const results = document.getElementById('searchResults');
            const query = input.value.trim();
            
            if (!query) {
                results.innerHTML = '<div class="result error">请输入搜索关键词</div>';
                return;
            }
            
            results.innerHTML = '<div class="result">正在搜索...</div>';
            
            try {
                // 使用最简单的方式测试搜索
                const testUrl = '/proxy/' + encodeURIComponent('https://api.allorigins.win/raw?url=https://httpbin.org/json');
                
                console.log('测试URL:', testUrl);
                
                const response = await fetch(testUrl);
                
                if (response.ok) {
                    const data = await response.text();
                    results.innerHTML = `
                        <div class="result success">✓ 代理连接正常</div>
                        <div class="result">响应状态: ${response.status}</div>
                        <div class="result">响应类型: ${response.headers.get('content-type')}</div>
                        <div class="result">响应长度: ${data.length} 字符</div>
                    `;
                } else {
                    results.innerHTML = `<div class="result error">✗ 代理请求失败，状态码: ${response.status}</div>`;
                }
            } catch (error) {
                results.innerHTML = `<div class="result error">✗ 搜索测试失败: ${error.message}</div>`;
                console.error('搜索测试错误:', error);
            }
        }
        
        // API配置测试
        function testAPIConfig() {
            const results = document.getElementById('apiResults');
            results.innerHTML = '';
            
            // 检查基本配置
            const configs = [
                {
                    name: 'PROXY_URL',
                    value: '/proxy/',
                    check: () => true
                },
                {
                    name: 'API_CONFIG',
                    value: 'search.headers',
                    check: () => true
                },
                {
                    name: 'selectedAPIs',
                    value: localStorage.getItem('selectedAPIs') || '[]',
                    check: () => {
                        try {
                            JSON.parse(localStorage.getItem('selectedAPIs') || '[]');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    }
                }
            ];
            
            configs.forEach(config => {
                const isValid = config.check();
                const div = document.createElement('div');
                div.className = 'result ' + (isValid ? 'success' : 'error');
                div.innerHTML = `${config.name}: ${isValid ? '✓' : '✗'} ${config.value}`;
                results.appendChild(div);
            });
            
            // 显示用户代理
            const uaDiv = document.createElement('div');
            uaDiv.className = 'result';
            uaDiv.innerHTML = `User Agent: ${navigator.userAgent}`;
            results.appendChild(uaDiv);
        }
        
        // 页面加载时自动运行基础测试
        window.addEventListener('load', function() {
            testBasicFunctions();
        });
        
        // 监听错误
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e.error);
            const errorDiv = document.createElement('div');
            errorDiv.className = 'result error';
            errorDiv.innerHTML = `页面错误: ${e.message} (${e.filename}:${e.lineno})`;
            document.body.appendChild(errorDiv);
        });
        
        // 监听未处理的Promise拒绝
        window.addEventListener('unhandledrejection', function(e) {
            console.error('未处理的Promise拒绝:', e.reason);
            const errorDiv = document.createElement('div');
            errorDiv.className = 'result error';
            errorDiv.innerHTML = `Promise错误: ${e.reason}`;
            document.body.appendChild(errorDiv);
        });
    </script>
</body>
</html>
