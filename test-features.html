<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LibreTV 功能测试页面</title>
    <script src="libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="css/styles.css">
    <script src="js/config.js"></script>
    <script src="js/themes.js"></script>
    <script src="js/search.js"></script>
</head>
<body class="page-bg text-white min-h-screen">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold mb-8 text-center">LibreTV 功能测试</h1>
        
        <!-- 主题测试 -->
        <div class="mb-8 p-6 bg-[#111] rounded-lg border border-[#333]">
            <h2 class="text-xl font-semibold mb-4">主题系统测试</h2>
            <div id="themeTestGrid" class="grid grid-cols-2 md:grid-cols-3 gap-4">
                <!-- 主题选项将在这里动态生成 -->
            </div>
        </div>
        
        <!-- 搜索功能测试 -->
        <div class="mb-8 p-6 bg-[#111] rounded-lg border border-[#333]">
            <h2 class="text-xl font-semibold mb-4">模糊搜索测试</h2>
            <div class="mb-4">
                <input type="text" id="testSearchInput" 
                       class="w-full px-4 py-2 bg-[#222] border border-[#333] rounded-lg text-white"
                       placeholder="输入搜索关键词测试模糊搜索...">
                <button onclick="testFuzzySearch()" 
                        class="mt-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg">
                    测试搜索
                </button>
            </div>
            <div id="searchResults" class="mt-4">
                <!-- 搜索结果将在这里显示 -->
            </div>
        </div>
        
        <!-- API测试 -->
        <div class="mb-8 p-6 bg-[#111] rounded-lg border border-[#333]">
            <h2 class="text-xl font-semibold mb-4">API功能测试</h2>
            <div class="mb-4">
                <input type="text" id="testApiUrl" 
                       class="w-full px-4 py-2 bg-[#222] border border-[#333] rounded-lg text-white"
                       placeholder="输入自定义API地址测试...">
                <button onclick="testCustomApi()" 
                        class="mt-2 px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg">
                    测试API
                </button>
            </div>
            <div id="apiResults" class="mt-4">
                <!-- API测试结果将在这里显示 -->
            </div>
        </div>
        
        <!-- 状态显示 -->
        <div class="p-6 bg-[#111] rounded-lg border border-[#333]">
            <h2 class="text-xl font-semibold mb-4">系统状态</h2>
            <div id="systemStatus" class="space-y-2">
                <!-- 系统状态将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        // 初始化测试页面
        document.addEventListener('DOMContentLoaded', function() {
            initThemeSystem();
            renderThemeTestOptions();
            updateSystemStatus();
        });

        // 渲染主题测试选项
        function renderThemeTestOptions() {
            const grid = document.getElementById('themeTestGrid');
            if (!grid || !window.THEMES) return;

            Object.entries(THEMES).forEach(([key, theme]) => {
                const option = document.createElement('div');
                option.className = 'p-4 rounded-lg border-2 border-gray-600 cursor-pointer transition-all hover:border-gray-400';
                option.style.background = `linear-gradient(135deg, ${theme.colors.cardGradientStart}, ${theme.colors.cardGradientEnd})`;
                
                option.innerHTML = `
                    <div class="flex items-center space-x-2 mb-2">
                        <div class="w-4 h-4 rounded-full" style="background: ${theme.colors.primary}"></div>
                        <div class="w-3 h-3 rounded-full" style="background: ${theme.colors.accent}"></div>
                    </div>
                    <div class="text-sm font-medium text-white">${theme.name}</div>
                    <div class="text-xs text-gray-300 mt-1">${theme.description}</div>
                `;

                option.addEventListener('click', () => {
                    if (window.themeManager) {
                        window.themeManager.applyTheme(key);
                        updateSystemStatus();
                    }
                });

                grid.appendChild(option);
            });
        }

        // 测试模糊搜索
        function testFuzzySearch() {
            const input = document.getElementById('testSearchInput');
            const results = document.getElementById('searchResults');
            const query = input.value.trim();

            if (!query) {
                results.innerHTML = '<p class="text-yellow-400">请输入搜索关键词</p>';
                return;
            }

            // 模拟一些测试数据
            const testData = [
                { vod_name: '复仇者联盟', vod_year: '2012', vod_score: '8.5', vod_actor: '小罗伯特·唐尼' },
                { vod_name: '复仇者联盟2：奥创纪元', vod_year: '2015', vod_score: '7.8', vod_actor: '小罗伯特·唐尼' },
                { vod_name: '复仇者联盟3：无限战争', vod_year: '2018', vod_score: '8.7', vod_actor: '小罗伯特·唐尼' },
                { vod_name: '钢铁侠', vod_year: '2008', vod_score: '8.1', vod_actor: '小罗伯特·唐尼' },
                { vod_name: '钢铁侠2', vod_year: '2010', vod_score: '7.2', vod_actor: '小罗伯特·唐尼' },
                { vod_name: '蜘蛛侠：英雄归来', vod_year: '2017', vod_score: '7.9', vod_actor: '汤姆·赫兰德' },
                { vod_name: '美国队长', vod_year: '2011', vod_score: '7.5', vod_actor: '克里斯·埃文斯' },
                { vod_name: '雷神', vod_year: '2011', vod_score: '7.3', vod_actor: '克里斯·海姆斯沃斯' }
            ];

            if (typeof rankSearchResults === 'function') {
                const rankedResults = rankSearchResults(testData, query);
                
                results.innerHTML = `
                    <h3 class="text-lg font-semibold mb-2">搜索结果 (按相关性排序):</h3>
                    <div class="space-y-2">
                        ${rankedResults.map((item, index) => `
                            <div class="p-3 bg-[#222] rounded border border-[#333]">
                                <div class="font-medium">${index + 1}. ${item.vod_name}</div>
                                <div class="text-sm text-gray-400">
                                    年份: ${item.vod_year} | 评分: ${item.vod_score} | 主演: ${item.vod_actor}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            } else {
                results.innerHTML = '<p class="text-red-400">模糊搜索功能未加载</p>';
            }
        }

        // 测试自定义API
        function testCustomApi() {
            const input = document.getElementById('testApiUrl');
            const results = document.getElementById('apiResults');
            const url = input.value.trim();

            if (!url) {
                results.innerHTML = '<p class="text-yellow-400">请输入API地址</p>';
                return;
            }

            results.innerHTML = '<p class="text-blue-400">正在测试API连接...</p>';

            // 简单的URL格式验证
            try {
                new URL(url);
                results.innerHTML = `
                    <div class="space-y-2">
                        <p class="text-green-400">✓ URL格式正确</p>
                        <p class="text-gray-400">API地址: ${url}</p>
                        <p class="text-yellow-400">注意: 实际连接测试需要在完整环境中进行</p>
                    </div>
                `;
            } catch (e) {
                results.innerHTML = '<p class="text-red-400">✗ URL格式不正确</p>';
            }
        }

        // 更新系统状态
        function updateSystemStatus() {
            const status = document.getElementById('systemStatus');
            if (!status) return;

            const currentTheme = window.themeManager ? window.themeManager.getCurrentTheme() : 'unknown';
            const themeName = window.THEMES && window.THEMES[currentTheme] ? window.THEMES[currentTheme].name : '未知';

            status.innerHTML = `
                <div class="flex justify-between">
                    <span>当前主题:</span>
                    <span class="text-blue-400">${themeName}</span>
                </div>
                <div class="flex justify-between">
                    <span>主题系统:</span>
                    <span class="text-green-400">${window.themeManager ? '已加载' : '未加载'}</span>
                </div>
                <div class="flex justify-between">
                    <span>搜索功能:</span>
                    <span class="text-green-400">${typeof rankSearchResults === 'function' ? '已加载' : '未加载'}</span>
                </div>
                <div class="flex justify-between">
                    <span>API功能:</span>
                    <span class="text-green-400">${typeof handleApiRequest === 'function' ? '已加载' : '未加载'}</span>
                </div>
            `;
        }
    </script>
</body>
</html>
