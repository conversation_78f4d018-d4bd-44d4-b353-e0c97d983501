// 模糊搜索工具函数
function fuzzySearch(searchTerm, targetText) {
    if (!searchTerm || !targetText) return 0;

    const search = searchTerm.toLowerCase();
    const target = targetText.toLowerCase();

    // 完全匹配得分最高
    if (target === search) return 100;

    // 包含完整搜索词得分较高
    if (target.includes(search)) return 80;

    // 计算编辑距离相似度
    const editDistance = calculateEditDistance(search, target);
    const maxLength = Math.max(search.length, target.length);
    const similarity = ((maxLength - editDistance) / maxLength) * 60;

    // 检查关键词匹配
    const searchWords = search.split(/\s+/).filter(word => word.length > 0);
    const targetWords = target.split(/\s+/).filter(word => word.length > 0);

    let wordMatchScore = 0;
    let matchedWords = 0;

    searchWords.forEach(searchWord => {
        targetWords.forEach(targetWord => {
            if (targetWord.includes(searchWord) || searchWord.includes(targetWord)) {
                matchedWords++;
                if (targetWord === searchWord) {
                    wordMatchScore += 20;
                } else if (targetWord.includes(searchWord)) {
                    wordMatchScore += 15;
                } else {
                    wordMatchScore += 10;
                }
            }
        });
    });

    // 综合得分
    const finalScore = Math.max(similarity, wordMatchScore);
    return Math.min(finalScore, 100);
}

// 计算编辑距离（Levenshtein距离）
function calculateEditDistance(str1, str2) {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
        matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
        matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
        for (let j = 1; j <= str1.length; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }

    return matrix[str2.length][str1.length];
}

// 关键词分词和处理
function processSearchKeywords(query) {
    if (!query) return [];

    // 移除特殊字符，保留中文、英文、数字和空格
    const cleanQuery = query.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, ' ');

    // 分词
    const words = cleanQuery.split(/\s+/).filter(word => word.length > 0);

    // 生成搜索变体
    const variants = new Set(words);

    // 添加组合词
    if (words.length > 1) {
        variants.add(words.join(''));
        variants.add(words.join(' '));
    }

    return Array.from(variants);
}

async function searchByAPIAndKeyWord(apiId, query) {
    try {
        let apiUrl, apiName, apiBaseUrl;
        
        // 处理自定义API
        if (apiId.startsWith('custom_')) {
            const customIndex = apiId.replace('custom_', '');
            const customApi = getCustomApiInfo(customIndex);
            if (!customApi) return [];
            
            apiBaseUrl = customApi.url;
            apiUrl = apiBaseUrl + API_CONFIG.search.path + encodeURIComponent(query);
            apiName = customApi.name;
        } else {
            // 内置API
            if (!API_SITES[apiId]) return [];
            apiBaseUrl = API_SITES[apiId].api;
            apiUrl = apiBaseUrl + API_CONFIG.search.path + encodeURIComponent(query);
            apiName = API_SITES[apiId].name;
        }
        
        // 添加超时处理 - 使用更兼容的方式
        let controller = null;
        let timeoutId = null;
        let isAborted = false;

        try {
            controller = new AbortController();
            timeoutId = setTimeout(() => {
                if (controller && !isAborted) {
                    try {
                        isAborted = true;
                        controller.abort();
                    } catch (e) {
                        console.warn('取消请求失败:', e);
                    }
                }
            }, 8000);
        } catch (e) {
            console.warn('AbortController 不支持，使用备用方案');
            controller = null;
        }
        
        let response;
        try {
            const fetchOptions = {
                headers: API_CONFIG.search.headers
            };

            // 只有在支持 AbortController 时才添加 signal
            if (controller && controller.signal) {
                fetchOptions.signal = controller.signal;
            }

            response = await fetch(PROXY_URL + encodeURIComponent(apiUrl), fetchOptions);
        } catch (fetchError) {
            if (timeoutId) clearTimeout(timeoutId);
            if (fetchError.name === 'AbortError') {
                console.warn(`API ${apiId} 搜索超时`);
            } else {
                console.warn(`API ${apiId} 网络请求失败:`, fetchError.message);
            }
            return [];
        }

        if (timeoutId) clearTimeout(timeoutId);

        if (!response.ok) {
            console.warn(`API ${apiId} 返回错误状态: ${response.status}`);
            return [];
        }

        let data;
        try {
            data = await response.json();
        } catch (parseError) {
            console.warn(`API ${apiId} JSON解析失败:`, parseError.message);
            return [];
        }
        
        if (!data || !data.list || !Array.isArray(data.list) || data.list.length === 0) {
            return [];
        }
        
        // 处理第一页结果
        const results = data.list.map(item => ({
            ...item,
            source_name: apiName,
            source_code: apiId,
            api_url: apiId.startsWith('custom_') ? getCustomApiInfo(apiId.replace('custom_', ''))?.url : undefined
        }));
        
        // 获取总页数
        const pageCount = data.pagecount || 1;
        // 确定需要获取的额外页数 (最多获取maxPages页)
        const pagesToFetch = Math.min(pageCount - 1, API_CONFIG.search.maxPages - 1);
        
        // 如果有额外页数，获取更多页的结果
        if (pagesToFetch > 0) {
            const additionalPagePromises = [];
            
            for (let page = 2; page <= pagesToFetch + 1; page++) {
                // 构建分页URL
                const pageUrl = apiBaseUrl + API_CONFIG.search.pagePath
                    .replace('{query}', encodeURIComponent(query))
                    .replace('{page}', page);
                
                // 创建获取额外页的Promise
                const pagePromise = (async () => {
                    try {
                        let pageController = null;
                        let pageTimeoutId = null;
                        let pageIsAborted = false;

                        try {
                            pageController = new AbortController();
                            pageTimeoutId = setTimeout(() => {
                                if (pageController && !pageIsAborted) {
                                    try {
                                        pageIsAborted = true;
                                        pageController.abort();
                                    } catch (e) {
                                        console.warn('取消分页请求失败:', e);
                                    }
                                }
                            }, 8000);
                        } catch (e) {
                            console.warn('分页请求 AbortController 不支持');
                            pageController = null;
                        }
                        
                        const pageFetchOptions = {
                            headers: API_CONFIG.search.headers
                        };

                        // 只有在支持 AbortController 时才添加 signal
                        if (pageController && pageController.signal) {
                            pageFetchOptions.signal = pageController.signal;
                        }

                        const pageResponse = await fetch(PROXY_URL + encodeURIComponent(pageUrl), pageFetchOptions);

                        if (pageTimeoutId) clearTimeout(pageTimeoutId);
                        
                        if (!pageResponse.ok) return [];
                        
                        const pageData = await pageResponse.json();
                        
                        if (!pageData || !pageData.list || !Array.isArray(pageData.list)) return [];
                        
                        // 处理当前页结果
                        return pageData.list.map(item => ({
                            ...item,
                            source_name: apiName,
                            source_code: apiId,
                            api_url: apiId.startsWith('custom_') ? getCustomApiInfo(apiId.replace('custom_', ''))?.url : undefined
                        }));
                    } catch (error) {
                        console.warn(`API ${apiId} 第${page}页搜索失败:`, error);
                        return [];
                    }
                })();
                
                additionalPagePromises.push(pagePromise);
            }
            
            // 等待所有额外页的结果
            const additionalResults = await Promise.all(additionalPagePromises);
            
            // 合并所有页的结果
            additionalResults.forEach(pageResults => {
                if (pageResults.length > 0) {
                    results.push(...pageResults);
                }
            });
        }

        // 应用模糊搜索排序
        const rankedResults = rankSearchResults(results, query);

        return rankedResults;
    } catch (error) {
        console.warn(`API ${apiId} 搜索失败:`, error);
        return [];
    }
}

// 对搜索结果进行模糊匹配排序
function rankSearchResults(results, query) {
    if (!results || results.length === 0 || !query) {
        return results;
    }

    const searchKeywords = processSearchKeywords(query);

    // 为每个结果计算相关性得分
    const scoredResults = results.map(item => {
        let maxScore = 0;
        const title = item.vod_name || '';
        const description = item.vod_blurb || item.vod_content || '';
        const actor = item.vod_actor || '';
        const director = item.vod_director || '';

        // 对每个搜索关键词计算得分
        searchKeywords.forEach(keyword => {
            // 标题匹配权重最高
            const titleScore = fuzzySearch(keyword, title) * 1.0;
            // 演员匹配权重较高
            const actorScore = fuzzySearch(keyword, actor) * 0.8;
            // 导演匹配权重中等
            const directorScore = fuzzySearch(keyword, director) * 0.7;
            // 描述匹配权重较低
            const descScore = fuzzySearch(keyword, description) * 0.5;

            const keywordScore = Math.max(titleScore, actorScore, directorScore, descScore);
            maxScore = Math.max(maxScore, keywordScore);
        });

        // 添加其他排序因素
        let bonusScore = 0;

        // 年份较新的内容加分
        const year = parseInt(item.vod_year) || 0;
        if (year >= 2020) bonusScore += 5;
        else if (year >= 2015) bonusScore += 3;
        else if (year >= 2010) bonusScore += 1;

        // 有评分的内容加分
        const score = parseFloat(item.vod_score) || 0;
        if (score >= 8.0) bonusScore += 3;
        else if (score >= 7.0) bonusScore += 2;
        else if (score >= 6.0) bonusScore += 1;

        return {
            ...item,
            _searchScore: maxScore + bonusScore
        };
    });

    // 按得分排序，得分相同时按年份排序
    scoredResults.sort((a, b) => {
        if (Math.abs(a._searchScore - b._searchScore) < 0.1) {
            // 得分相近时，按年份排序
            const yearA = parseInt(a.vod_year) || 0;
            const yearB = parseInt(b.vod_year) || 0;
            return yearB - yearA;
        }
        return b._searchScore - a._searchScore;
    });

    // 移除临时的得分字段
    return scoredResults.map(item => {
        const { _searchScore, ...cleanItem } = item;
        return cleanItem;
    });
}